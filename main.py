import aiohttp
import json
import asyncio
from astrbot.api.event import filter, AstrMessageEvent
from astrbot.api.star import Context, Star, register
from astrbot.api.message_components import Plain
from astrbot.api import logger

# ### <<< 修改部分 开始 ###
# 1. 新增API配置常量
# !!!重要!!! 请将 'http://your_api_address.com' 替换为你的实际API地址
API_URL = "https://osound.ykload.com/api/get-use-code"
API_TOKEN = "OSound798999."
# ### <<< 修改部分 结束 ###


# 配置常量
MAX_RESPONSE_SIZE = 10 * 1024 * 1024  # 10MB响应限制
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 1  # 初始重试延迟(秒)


async def send_request(url, method="GET", params=None, data=None, headers=None, cookies=None):
    """发送HTTP请求并返回结果，支持自动重试和响应大小限制"""
    retries = 0
    last_error = None
    
    while retries <= MAX_RETRIES:
        try:
            async with aiohttp.ClientSession(cookies=cookies) as session:
                kwargs = {
                    'params': params,
                    'headers': headers,
                    'timeout': aiohttp.ClientTimeout(total=10)
                }
                
                if data:
                    if isinstance(data, dict):
                        kwargs['json'] = data
                    else:
                        kwargs['data'] = data
                
                http_method = getattr(session, method.lower(), None)
                if not http_method:
                    return {"success": False, "message": f"不支持的请求方法: {method}"}
                
                async with http_method(url, **kwargs) as response:
                    response.raise_for_status()
                    
                    content_length = response.headers.get('Content-Length')
                    if content_length and int(content_length) > MAX_RESPONSE_SIZE:
                        return {
                            "success": False, 
                            "message": f"响应数据过大: {int(content_length)/1024/1024:.2f}MB (最大限制: {MAX_RESPONSE_SIZE/1024/1024}MB)",
                            "status_code": response.status
                        }
                    
                    data = bytearray()
                    chunk_size = 8192
                    async for chunk in response.content.iter_chunked(chunk_size):
                        data.extend(chunk)
                        if len(data) > MAX_RESPONSE_SIZE:
                            return {
                                "success": False, 
                                "message": f"响应数据超过限制: {MAX_RESPONSE_SIZE/1024/1024}MB",
                                "status_code": response.status
                            }
                    
                    try:
                        result = json.loads(data.decode('utf-8'))
                        return {"success": True, "data": result, "status_code": response.status}
                    except:
                        return {"success": True, "data": data.decode('utf-8'), "status_code": response.status}
        
        except aiohttp.ClientResponseError as http_err:
            return {"success": False, "message": f"HTTP错误: {http_err}", "status_code": http_err.status}
        except (aiohttp.ClientConnectorError, aiohttp.ServerTimeoutError, aiohttp.ClientOSError) as e:
            retries += 1
            last_error = e
            if retries <= MAX_RETRIES:
                await asyncio.sleep(RETRY_DELAY * retries)
                logger.info(f"重试请求({retries}/{MAX_RETRIES}): {url}")
                continue
            else:
                return {"success": False, "message": f"请求失败(已重试{MAX_RETRIES}次): {str(last_error)}"}
        except Exception as e:
            return {"success": False, "message": f"请求错误: {str(e)}"}

# ### <<< 修改部分 开始 ###
# 3. 删除了不再需要的 parse_curl_command 函数
# ### <<< 修改部分 结束 ###

@register("http_request", "wayzinx", "授权查询插件", "2.0.0") # 修改了插件描述和版本
class HttpRequestPlugin(Star):
    def __init__(self, context: Context):
        super().__init__(context)

    async def summarize_response(self, result):
        """直接使用预处理的文案格式化响应内容"""
        if not result["success"]:
            # 对用户更友好地展示错误信息
            if result.get("status_code"):
                 return f"请求失败: 目标服务器返回错误 (状态码: {result['status_code']})。\n详情: {result['message']}"
            return f"请求失败: {result['message']}"

        data = result.get('data', {})

        # 直接使用预处理的文案格式化响应
        try:
            if isinstance(data, dict):
                # 处理授权码类型的响应 (accessCode + expiresAt)
                if 'accessCode' in data and 'expiresAt' in data:
                    access_code = data.get('accessCode', '未获取到')
                    expires_at = data.get('expiresAt', '未获取到')
                    return f"查到啦！\n使用码: {access_code}\n{expires_at} 前有效哦~"

                # 处理登录码类型的响应 (success + loginCode + expiresIn)
                elif 'loginCode' in data and 'expiresIn' in data:
                    login_code = data.get('loginCode', '未获取到')
                    expires_in = data.get('expiresIn', 0)
                    action = data.get('action', '')

                    # 根据action字段判断用户类型
                    if action == 'newUserLogin':
                        return f"查到啦！\n登录码: {login_code}\n你似乎是第一次用FlowSync呢\n请尽快在{expires_in}秒内使用哦~"
                    elif action == 'existingUserLogin':
                        return f"查到啦！\n登录码: {login_code}\n是要在第二台设备上登录么？\n请尽快在{expires_in}秒内使用哦~"
                    else:
                        # 默认格式
                        return f"查到啦！\n登录码: {login_code}\n请尽快在{expires_in}秒内使用哦~"

                # 处理其他格式的响应
                else:
                    # 尝试提取常见字段
                    if 'code' in data:
                        code = data.get('code', '未获取到')
                        return f"查到啦！\n代码: {code}"
                    elif 'message' in data:
                        message = data.get('message', '')
                        return f"服务器回复: {message}"
                    else:
                        return f"查询完成！\n{str(data)[:500]}"
            else:
                return f"查询完成！\n{str(data)[:500]}"

        except Exception as e:
            logger.error(f"格式化响应内容时出错: {e}")
            return f"格式化失败，原始信息：\n{str(data)[:500]}"

    async def summarize_osound_response(self, result):
        """专门用于OSound服务的响应格式化"""
        if not result["success"]:
            return f"OSound查询失败: {result.get('message', '未知错误')}"

        data = result.get('data', {})

        try:
            if isinstance(data, dict):
                # OSound通常返回accessCode和expiresAt
                if 'accessCode' in data and 'expiresAt' in data:
                    access_code = data.get('accessCode', '未获取到')
                    expires_at = data.get('expiresAt', '未获取到')
                    return f"查到啦！\nOSound使用码: {access_code}\n有效期至: {expires_at}\n快去自定义提示音吧~"
                elif 'code' in data:
                    code = data.get('code', '未获取到')
                    return f"OSound使用码: {code}\n快去自定义提示音吧~"
                else:
                    return f"OSound查询完成！\n{str(data)[:300]}"
            else:
                return f"OSound查询完成！\n{str(data)[:300]}"
        except Exception as e:
            logger.error(f"OSound响应格式化出错: {e}")
            return f"OSound查询完成，但格式化失败\n原始信息：{str(data)[:300]}"

    async def summarize_seeq_response(self, result):
        """专门用于Seeq服务的响应格式化"""
        if not result["success"]:
            return f"Seeq注册失败: {result.get('message', '未知错误')}"

        data = result.get('data', {})

        try:
            if isinstance(data, dict):
                # Seeq登录码相关
                if 'loginCode' in data:
                    login_code = data.get('loginCode', '未获取到')
                    expires_in = data.get('expiresIn', 60)
                    success = data.get('success', True)

                    if success:
                        return f"查到啦！\nSeeq登录码: {login_code}\n请在{expires_in}秒内完成登录\n开始你的Eq探索之旅吧~"
                    else:
                        return f"Seeq登录码: {login_code}\n但可能存在问题，请尽快使用"
                elif 'message' in data:
                    message = data.get('message', '')
                    return f"Seeq服务回复: {message}"
                else:
                    return f"Seeq注册完成！\n{str(data)[:300]}"
            else:
                return f"Seeq注册完成！\n{str(data)[:300]}"
        except Exception as e:
            logger.error(f"Seeq响应格式化出错: {e}")
            return f"Seeq注册完成，但格式化失败\n原始信息：{str(data)[:300]}"

    async def summarize_flowsync_response(self, result):
        """专门用于FlowSync服务的响应格式化"""
        if not result["success"]:
            return f"FlowSync注册失败: {result.get('message', '未知错误')}"

        data = result.get('data', {})

        try:
            if isinstance(data, dict):
                # FlowSync登录码相关，支持新用户和老用户区分
                if 'loginCode' in data:
                    login_code = data.get('loginCode', '未获取到')
                    expires_in = data.get('expiresIn', 60)
                    action = data.get('action', '')

                    if action == 'newUserLogin':
                        return f"查到啦！\nFlowSync登录码: {login_code}\n欢迎新用户！这是你的第一次FlowSync体验\n请在{expires_in}秒内完成登录，开启同步之旅~"
                    elif action == 'existingUserLogin':
                        return f"查到啦！\nFlowSync登录码: {login_code}\n检测到你要在新设备上登录\n请在{expires_in}秒内完成登录，继续你的同步体验~"
                    else:
                        return f"查到啦！\nFlowSync登录码: {login_code}\n请在{expires_in}秒内完成登录\n享受无缝同步体验~"
                elif 'message' in data:
                    message = data.get('message', '')
                    return f"FlowSync服务回复: {message}"
                else:
                    return f"FlowSync注册完成！\n{str(data)[:300]}"
            else:
                return f"FlowSync注册完成！\n{str(data)[:300]}"
        except Exception as e:
            logger.error(f"FlowSync响应格式化出错: {e}")
            return f"FlowSync注册完成，但格式化失败\n原始信息：{str(data)[:300]}"

    
    # OSound 查询使用码
    @filter.command("，使用码，欧内盖~", "auth")
    async def query_auth_code(self, event: AstrMessageEvent):
        """发送固定请求到API以获取使用码和到期时间，并总结响应。"""

        yield event.chain_result([Plain("好哒，很快喔~")])

        # 准备请求头
        headers = {
            'Authorization': f'Bearer {API_TOKEN}'
        }

        # 发送固定的GET请求
        result = await send_request(
            url=API_URL,
            method="GET",
            headers=headers
        )

        # 获取OSound专用格式化后的回复
        response_text = await self.summarize_osound_response(result)

        # 将最终结果发送给用户
        yield event.chain_result([Plain(response_text)])

    # Seeq 注册登录
    @filter.command("，登录码，欧内盖~", "auth")
    async def register_seeq_user(self, event: AstrMessageEvent):
        """发送POST请求到注册API进行用户注册"""


        # 如果不是私聊，则要求用户私聊发送
        if not event.is_private_chat():
            yield event.chain_result([Plain("小心有坏蛋，私聊咱发送命令吧~")])
            return

        yield event.chain_result([Plain("好哒，很快喔~")])
        
        # 准备请求数据
        request_data = {
            'token': 'Seeq233UserManege',
            'qq': event.get_sender_id()  # 使用发送消息的用户QQ号
        }

        # 准备请求头
        headers = {
            'Content-Type': 'application/json'
        }

        # 发送POST请求到注册API
        result = await send_request(
            url='https://seeq.ykload.com/api/register',  # 根据需要修改为实际的注册API地址
            method="POST",
            data=request_data,
            headers=headers
        )

        # 获取Seeq专用格式化后的回复
        response_text = await self.summarize_seeq_response(result)

        # 将最终结果发送给用户
        yield event.chain_result([Plain(response_text)])


    # FlowSync 注册登录
    @filter.command("，我真的好想FlowSync", "auth")
    async def register_flowsync_user(self, event: AstrMessageEvent):
        """发送POST请求到注册API进行用户注册"""


        # 如果不是私聊，则要求用户私聊发送
        if not event.is_private_chat():
            yield event.chain_result([Plain("小心有坏蛋，私聊咱发送命令吧~")])
            return

        yield event.chain_result([Plain("好哒，很快喔~")])
        
        # 准备请求数据
        request_data = {
            'token': 'Flowmix.FlowSync777-MahiroAndYKload233',
            'qq': event.get_sender_id()  # 使用发送消息的用户QQ号
        }

        # 准备请求头
        headers = {
            'Content-Type': 'application/json'
        }

        # 发送POST请求到注册API
        result = await send_request(
            url='https://flowsync.ykload.com/api/register',  # 根据需要修改为实际的注册API地址
            method="POST",
            data=request_data,
            headers=headers
        )

        # 获取FlowSync专用格式化后的回复
        response_text = await self.summarize_flowsync_response(result)

        # 将最终结果发送给用户
        yield event.chain_result([Plain(response_text)])


    async def terminate(self):
        pass